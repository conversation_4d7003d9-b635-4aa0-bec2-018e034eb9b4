package service

import (
	"github.com/SneatX/adeptos_go/internal/domain"
	"github.com/SneatX/adeptos_go/internal/ports"
)

type LeadService struct {
	leadRepo   ports.LeanRepository
	gptService ports.GPTService
}

func NewLeadService(leadRepo ports.LeanRepository, gptService ports.GPTService) *LeadService {
	return &LeadService{
		leadRepo:   leadRepo,
		gptService: gptService,
	}
}

func (s *LeadService) CreateLeadAndGenerateResponse(lead *domain.Lead) (*ports.CreateLeadOutput, error) {
	if _, err := s.leadRepo.Create(lead); err != nil {
		return nil, err
	}

	input := ports.LeadInput{
		FullName:           lead.FullName,
		CompanyName:        lead.CompanyName,
		Email:              lead.Email,
		Phone:              lead.Phone,
		Website:            lead.Website,
		Industry:           lead.Industry,
		EmployeeCount:      lead.EmployeeCount,
		BusinessType:       lead.BusinessType,
		MonthlyRevenue:     lead.MonthlyRevenue,
		SalesLocation:      lead.SalesLocation,
		TimeConsumingTasks: lead.TimeConsumingTasks,
		Bottlenecks:        lead.Bottlenecks,
	}

	response, err := s.gptService.GenerateResponse(input)
	if err != nil {
		return nil, err
	}

	return &ports.CreateLeadOutput{
		Lead:        lead,
		GPTResponse: response,
	}, nil

}
