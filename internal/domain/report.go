package domain

import (
	"time"

	"gorm.io/datatypes"
)

// ReportData represents the structured data from OpenAI API responses
type ReportData struct {
	AIReadinessScore int    `json:"aiReadinessScore"`
	ScoreMessage     string `json:"scoreMessage"`
	ProjectedImpact  struct {
		Type  string `json:"type"`
		Value string `json:"value"`
	} `json:"projectedImpact"`
	WasteAreas    []string `json:"wasteAreas"`
	Opportunities []struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	} `json:"opportunities"`
}

// Report represents a report entity that stores OpenAI API responses
type Report struct {
	ID        uint           `gorm:"primaryKey" json:"id"`
	LeadID    uint           `gorm:"not null;index" json:"leadId"`
	Data      datatypes.JSON `gorm:"type:jsonb;not null" json:"data"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
}
