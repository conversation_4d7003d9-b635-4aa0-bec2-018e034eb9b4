package config

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
)

type DBConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
}

type ServerConfig struct {
	Port    string
	Hosting string
}

type OPENAIConfig struct {
	APIKey string
}

type envConfig struct {
	DBConfig
	ServerConfig
	OPENAIConfig
}

func getEnv(key, fallBack string) string {
	value, exist := os.LookupEnv(key)
	if exist {
		return value
	}
	fmt.Printf("Environment variable %q not found. Using default: %s\n", key, fallBack)
	return fallBack
}

func LoadEnv() *envConfig {
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	return &envConfig{
		DBConfig: DBConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			DBName:   getEnv("DB_NAME", "database"),
			User:     getEnv("DB_USER", "user"),
			Password: getEnv("DB_PASSWORD", "password"),
		},
		ServerConfig: ServerConfig{
			Port:    getEnv("SERVER_PORT", "8080"),
			Hosting: getEnv("SERVER_HOSTING", "localhost"),
		},
		OPENAIConfig: OPENAIConfig{
			APIKey: getEnv("OPENAI_API_KEY", "no-api-key"),
		},
	}
}
