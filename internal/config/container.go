package config

import (
	"github.com/SneatX/adeptos_go/internal/adapters/http/handlers"
	"github.com/SneatX/adeptos_go/internal/adapters/openai"
	"github.com/SneatX/adeptos_go/internal/repo"
	"github.com/SneatX/adeptos_go/internal/service"
	"gorm.io/gorm"
)

type Handlers struct {
	LeadHandler *handlers.LeadHandler
}

type Services struct {
	LeadService *service.LeadService
}

type Repositories struct {
	LeadRepo *repo.LeadRepo
}

type Container struct {
	DB           *gorm.DB
	Repositories *Repositories
	Services     *Services
	Handlers     *Handlers
}

func BuildContainer(env *envConfig) *Container {
	DB := InitDatabase(&env.DBConfig)

	leadRepo := repo.NewLeadRepo(DB)
	gptService := openai.NewOpenAIService(env.OPENAIConfig.APIKey)
	leadService := service.NewLeadService(leadRepo, gptService)
	leadHandler := handlers.NewLeadHandler(leadService)

	return &Container{
		DB: DB,
		Repositories: &Repositories{
			LeadRepo: leadRepo,
		},
		Services: &Services{
			LeadService: leadService,
		},
		Handlers: &Handlers{
			LeadHandler: leadHandler,
		},
	}
}
