package openai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"

	"github.com/SneatX/adeptos_go/internal/domain"
	"github.com/SneatX/adeptos_go/internal/ports"
	openai "github.com/sasha<PERSON>nov/go-openai"
)

type OpenAIService struct {
	client *openai.Client
}

func NewOpenAIService(apiKey string) *OpenAIService {
	client := openai.NewClient(apiKey)
	return &OpenAIService{client: client}
}

func RenderTemplate(relPath string, data any) (string, error) {
	abs := filepath.Join("internal", "adapters", "openai", "prompts", relPath)

	raw, err := os.ReadFile(abs)
	if err != nil {
		return "", err
	}

	tmpl, err := template.New(filepath.Base(relPath)).Parse(string(raw))
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func (s *OpenAIService) GenerateResponse(input ports.LeadInput) (*domain.Report, error) {
	systemPrompt, err := RenderTemplate("lead_system.tmpl", nil)
	if err != nil {
		return nil, err
	}

	userPrompt, err := RenderTemplate("lead_user.tmpl", input)
	if err != nil {
		return nil, err
	}

	resp, err := s.client.CreateChatCompletion(
		context.Background(),
		openai.ChatCompletionRequest{
			Model: "gpt-4.1-mini",
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleSystem,
					Content: systemPrompt,
				},
				{
					Role:    openai.ChatMessageRoleUser,
					Content: userPrompt,
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}

	if len(resp.Choices) == 0 {
		return nil, fmt.Errorf("empty response from OpenAI")
	}

	var report *domain.Report
	content := strings.TrimSpace(resp.Choices[0].Message.Content)

	if err := json.Unmarshal([]byte(content), &report); err != nil {
		return nil, fmt.Errorf("invalid JSON from OpenAI: %w", err)
	}

	return report, nil
}
